rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للمستخدمين المصادق عليهم
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // قواعد للأدمن
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         resource.data.email == request.auth.token.email);
    }

    // قواعد ملفات PDF - مؤقتاً للاختبار (مفتوحة للجميع)
    match /pdfs/{pdfId} {
      // السماح بالقراءة للجميع مؤقتاً للاختبار
      allow read: if true;

      // السماح بالكتابة للجميع مؤقتاً للاختبار
      allow create, update: if true;

      // السماح بالحذف للجميع مؤقتاً للاختبار
      allow delete: if true;
    }

    // قواعد الإشعارات - محسنة للأمان
    match /notifications/{notificationId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالكتابة للأدمن فقط
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد المنشورات - محسنة للأمان والأداء
    match /posts/{postId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.authorId == request.auth.uid &&
        request.resource.data.keys().hasAll(['content', 'authorId', 'createdAt']);

      // السماح بالتحديث لصاحب المنشور أو الأدمن
      allow update: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');

      // السماح بالحذف لصاحب المنشور أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد التعليقات - محسنة
    match /comments/{commentId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.authorId == request.auth.uid;

      allow update, delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد الإعجابات - محسنة
    match /likes/{likeId} {
      allow read: if true;

      allow create, delete: if request.auth != null &&
        request.auth.uid == likeId.split('_')[0];
    }

    // قواعد المشاركات - محسنة
    match /shares/{shareId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // قواعد الاستطلاعات - محسنة
    match /polls/{pollId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.createdBy == request.auth.uid;

      allow update, delete: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد أصوات الاستطلاعات - محسنة
    match /poll_votes/{voteId} {
      allow read: if request.auth != null;

      allow create, update: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      allow delete: if request.auth != null &&
        resource.data.userId == request.auth.uid;
    }

    // قواعد الدردشة - محسنة للأمان والأداء
    match /chats/{chatId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالكتابة للمستخدمين المسجلين (إنشاء غرف دردشة)
      allow create: if request.auth != null &&
        request.resource.data.keys().hasAll(['name', 'type', 'createdAt']);

      // السماح بالتحديث للأدمن فقط
      allow update: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';

      // منع الحذف (الاحتفاظ بسجل الدردشة)
      allow delete: if false;
    }

    // قواعد رسائل الدردشة - محسنة
    match /chat_messages/{messageId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.senderId == request.auth.uid &&
        request.resource.data.keys().hasAll(['content', 'senderId', 'chatId', 'timestamp']);

      // السماح بالتحديث لصاحب الرسالة (تعديل الرسالة)
      allow update: if request.auth != null &&
        resource.data.senderId == request.auth.uid;

      // السماح بالحذف لصاحب الرسالة أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.senderId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // السماح بالقراءة والكتابة للتسجيلات المؤقتة
    match /pending_registrations/{email} {
      allow read, write: if true;
    }

    // قواعد FCM Tokens - مطلوبة للإشعارات
    match /fcm_tokens/{tokenId} {
      allow read, write: if request.auth != null;
    }

    // قواعد تحقق البريد الإلكتروني
    match /email_verifications/{document} {
      allow read, write: if true;
    }

    // قواعد فئات المنشورات
    match /post_categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد عامة للمجموعات الأخرى (مؤقتاً للاختبار)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
