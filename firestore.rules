rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد الأدمن
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد المنشورات - الأهم للمجتمع
    match /posts/{postId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null;

      // السماح بالتحديث لصاحب المنشور أو الأدمن
      allow update: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');

      // السماح بالحذف لصاحب المنشور أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد التعليقات
    match /comments/{commentId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد الإعجابات
    match /likes/{likeId} {
      allow read: if true;
      allow create, delete: if request.auth != null;
    }

    // قواعد المشاركات
    match /shares/{shareId} {
      allow read: if true;
      allow create: if request.auth != null;
    }

    // قواعد الاستطلاعات
    match /polls/{pollId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد أصوات الاستطلاعات
    match /poll_votes/{voteId} {
      allow read: if request.auth != null;
      allow create, update: if request.auth != null;
      allow delete: if request.auth != null &&
        resource.data.userId == request.auth.uid;
    }

    // قواعد فئات المنشورات
    match /post_categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد الدردشة
    match /chats/{chatId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
      allow delete: if false; // منع الحذف
    }

    // قواعد رسائل الدردشة
    match /chat_messages/{messageId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update: if request.auth != null &&
        resource.data.senderId == request.auth.uid;
      allow delete: if request.auth != null &&
        (resource.data.senderId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد ملفات PDF
    match /pdfs/{pdfId} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد الإشعارات
    match /notifications/{notificationId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // قواعد FCM Tokens للإشعارات
    match /fcm_tokens/{tokenId} {
      allow read, write: if request.auth != null;
    }

    // قواعد التحقق من البريد الإلكتروني
    match /email_verifications/{document} {
      allow read, write: if true;
    }

    // قواعد التسجيلات المؤقتة
    match /pending_registrations/{email} {
      allow read, write: if true;
    }

    // قواعد عامة للمجموعات الأخرى (مؤقتاً للاختبار)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
