import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../providers/theme_provider.dart';
import '../models/post_model.dart';
import '../models/interaction_models.dart';
import '../services/post_service.dart';
import '../widgets/post_widget.dart';
import '../utils/logger.dart';
import 'create_post_screen.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  final List<PostModel> _posts = [];
  DocumentSnapshot? _lastDocument;
  bool _isLoading = false;
  bool _hasMorePosts = true;
  String _selectedCategory = 'الكل';
  List<PostCategoryModel> _categories = [];

  // Stream subscription for real-time updates
  Stream<List<PostModel>>? _postsStream;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCategories();
    _loadPosts();
    _setupScrollListener();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  void _loadCategories() async {
    final categories = await PostService.getCategories();
    setState(() {
      _categories = [
        PostCategoryModel(
          id: 'all',
          name: 'الكل',
          description: 'جميع المنشورات',
          icon: '📋',
          color: '#6B7280',
        ),
        ...categories,
      ];
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        if (!_isLoading && _hasMorePosts) {
          _loadMorePosts();
        }
      }
    });
  }

  void _loadPosts() {
    setState(() {
      _isLoading = true;
      _posts.clear();
      _lastDocument = null;
      _hasMorePosts = true;
    });

    // استخدام Stream للحصول على تحديثات فورية
    _postsStream = PostService.getPostsStream(
      category: _selectedCategory == 'الكل' ? null : _selectedCategory,
      limit: 20,
    );

    setState(() {
      _isLoading = false;
    });
  }

  void _loadMorePosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newPosts = await PostService.getPosts(
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
        limit: 10,
        lastDocument: _lastDocument,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _posts.clear();
          }
          _posts.addAll(newPosts);
          _hasMorePosts = newPosts.length == 10;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل المنشورات', 'CommunityScreen', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildBody(themeProvider),
                ),
              );
            },
          ),
          floatingActionButton: _buildFloatingActionButton(themeProvider),
        );
      },
    );
  }

  Widget _buildBody(ThemeProvider themeProvider) {
    return SafeArea(
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildHeader(themeProvider),
          _buildCategoryFilter(themeProvider),
          _buildPostsList(themeProvider),
          if (_isLoading) _buildLoadingIndicator(themeProvider),
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 160,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6366F1)],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مجتمع الطلاب',
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'شارك وتفاعل مع زملائك',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.people,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuickStat('12', 'منشورات'),
                  _buildQuickStat('48', 'متابع'),
                  _buildQuickStat('125', 'طالب نشط'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        margin: const EdgeInsets.symmetric(vertical: 16),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final isSelected = _selectedCategory == category.name;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category.name;
                });
                _loadPosts();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient:
                      isSelected
                          ? const LinearGradient(
                            colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                          )
                          : null,
                  color:
                      isSelected
                          ? null
                          : (themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(category.icon, style: const TextStyle(fontSize: 16)),
                    const SizedBox(width: 8),
                    Text(
                      category.name,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected
                                ? Colors.white
                                : (themeProvider.isDarkMode
                                    ? Colors.white
                                    : Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPostsList(ThemeProvider themeProvider) {
    if (_postsStream == null) {
      return SliverToBoxAdapter(child: _buildEmptyState(themeProvider));
    }

    return StreamBuilder<List<PostModel>>(
      stream: _postsStream,
      builder: (context, snapshot) {
        // إضافة تشخيص للـ Stream
        print('🔍 StreamBuilder State: ${snapshot.connectionState}');
        print('🔍 Has Error: ${snapshot.hasError}');
        print('🔍 Has Data: ${snapshot.hasData}');
        if (snapshot.hasData) {
          print('🔍 Posts Count: ${snapshot.data!.length}');
        }
        if (snapshot.hasError) {
          print('❌ Stream Error: ${snapshot.error}');
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return SliverToBoxAdapter(
            child: _buildLoadingIndicator(themeProvider),
          );
        }

        if (snapshot.hasError) {
          return SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color:
                        themeProvider.isDarkMode ? Colors.red[300] : Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في تحميل المنشورات',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.grey[400]
                              : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadPosts,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10B981),
                      foregroundColor: Colors.white,
                    ),
                    child: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
                  ),
                ],
              ),
            ),
          );
        }

        final posts = snapshot.data ?? [];

        if (posts.isEmpty) {
          return SliverToBoxAdapter(child: _buildEmptyState(themeProvider));
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            final post = posts[index];
            return PostWidget(
              post: post,
              onPostUpdated: () {
                // لا حاجة لإعادة تحميل المنشورات لأن Stream سيتحدث تلقائياً
              },
            );
          }, childCount: posts.length),
        );
      },
    );
  }

  Widget _buildEmptyState(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              ),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.people_outline,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'مرحباً بك في مجتمع Legal 2025!',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'كن أول من يشارك منشوراً في هذا المجتمع',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF10B981),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(ThemeProvider themeProvider) {
    return FloatingActionButton.extended(
      onPressed: _navigateToCreatePost,
      backgroundColor: const Color(0xFF10B981),
      foregroundColor: Colors.white,
      elevation: 8,
      icon: const Icon(Icons.add),
      label: Text(
        'منشور جديد',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
    );
  }

  void _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreatePostScreen()),
    );

    if (result == true) {
      // إعادة تحميل المنشورات بعد إنشاء منشور جديد
      _loadPosts();

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم نشر المنشور بنجاح!', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}
